<template>
  <div class="mobile-profile">
    <!-- 用户信息头部 -->
    <div class="profile-header">
      <div class="user-info">
        <img :src="userInfo.avatar" alt="头像" class="avatar" @click="changeAvatar" />
        <div class="info">
          <div class="name">{{ userInfo.name }}</div>
          <div class="role">{{ userInfo.role }}</div>
          <div class="department">{{ userInfo.department }}</div>
        </div>
      </div>
      <div class="edit-btn" @click="editProfile">
        <i class="el-icon-edit"></i>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="stat-item">
        <div class="value">{{ userStats.loginDays }}</div>
        <div class="label">登录天数</div>
      </div>
      <div class="stat-item">
        <div class="value">{{ userStats.processedOrders }}</div>
        <div class="label">处理订单</div>
      </div>
      <div class="stat-item">
        <div class="value">{{ userStats.approvalCount }}</div>
        <div class="label">审批次数</div>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <div class="menu-group">
        <div class="menu-item" @click="goToSettings">
          <div class="menu-left">
            <i class="el-icon-setting"></i>
            <span>账户设置</span>
          </div>
          <i class="el-icon-arrow-right"></i>
        </div>
        <div class="menu-item" @click="goToSecurity">
          <div class="menu-left">
            <i class="el-icon-lock"></i>
            <span>安全中心</span>
          </div>
          <i class="el-icon-arrow-right"></i>
        </div>
        <div class="menu-item" @click="goToNotification">
          <div class="menu-left">
            <i class="el-icon-bell"></i>
            <span>消息通知</span>
          </div>
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item" @click="goToHelp">
          <div class="menu-left">
            <i class="el-icon-question"></i>
            <span>帮助中心</span>
          </div>
          <i class="el-icon-arrow-right"></i>
        </div>
        <div class="menu-item" @click="goToFeedback">
          <div class="menu-left">
            <i class="el-icon-chat-dot-round"></i>
            <span>意见反馈</span>
          </div>
          <i class="el-icon-arrow-right"></i>
        </div>
        <div class="menu-item" @click="goToAbout">
          <div class="menu-left">
            <i class="el-icon-info"></i>
            <span>关于我们</span>
          </div>
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item" @click="clearCache">
          <div class="menu-left">
            <i class="el-icon-delete"></i>
            <span>清除缓存</span>
          </div>
          <div class="cache-size">{{ cacheSize }}</div>
        </div>
      </div>
    </div>

    <!-- 退出登录按钮 -->
    <div class="logout-section">
      <van-button type="danger" size="large" block class="logout-btn" @click="handleLogout">
        退出登录
      </van-button>
    </div>

    <!-- 版本信息 -->
    <div class="version-info">
      <p>版本号: v{{ version }}</p>
      <p>最后更新: {{ lastUpdate }}</p>
    </div>

    <!-- 编辑个人信息弹窗 -->
    <van-popup
      v-model="editDialogVisible"
      position="bottom"
      :style="{ height: '60%' }"
      round
      closeable
      close-icon-position="top-right"
    >
      <div class="edit-popup">
        <div class="popup-header">
          <h3>编辑个人信息</h3>
        </div>
        <div class="popup-content">
          <van-form @submit="saveProfile">
            <van-field
              v-model="editForm.name"
              name="name"
              label="姓名"
              placeholder="请输入姓名"
              :rules="[{ required: true, message: '请输入姓名' }]"
            />
            <van-field
              v-model="editForm.phone"
              name="phone"
              label="手机号"
              placeholder="请输入手机号"
              :rules="[
                { required: true, message: '请输入手机号' },
                { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
              ]"
            />
            <van-field
              v-model="editForm.email"
              name="email"
              label="邮箱"
              placeholder="请输入邮箱"
              :rules="[
                { required: true, message: '请输入邮箱' },
                { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }
              ]"
            />
            <div class="popup-footer">
              <van-button block type="primary" native-type="submit">
                保存
              </van-button>
            </div>
          </van-form>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Avatar from '@/assets/images/avatar.png'

export default {
  name: 'MobileProfile',
  data() {
    return {
      userInfo: {
        avatar: Avatar,
        name: '张三',
        role: '系统管理员',
        department: '技术部',
        phone: '138****8888',
        email: '<EMAIL>'
      },
      userStats: {
        loginDays: 365,
        processedOrders: 1234,
        approvalCount: 567
      },
      cacheSize: '12.5MB',
      version: '1.0.0',
      lastUpdate: '2024-01-15',
      editDialogVisible: false,
      editForm: {
        name: '',
        phone: '',
        email: ''
      },
      editRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    changeAvatar() {
      this.$toast('头像更换功能开发中...')
    },

    editProfile() {
      this.editForm = {
        name: this.userInfo.name,
        phone: this.userInfo.phone,
        email: this.userInfo.email
      }
      this.editDialogVisible = true
    },

    saveProfile(values) {
      // 这里调用API保存用户信息
      this.userInfo.name = values.name
      this.userInfo.phone = values.phone
      this.userInfo.email = values.email

      this.editDialogVisible = false
      this.$toast.success('保存成功')
    },

    goToSettings() {
      this.$toast('账户设置功能开发中...')
    },

    goToSecurity() {
      this.$toast('安全中心功能开发中...')
    },

    goToNotification() {
      this.$toast('消息通知功能开发中...')
    },

    goToHelp() {
      this.$toast('帮助中心功能开发中...')
    },

    goToFeedback() {
      this.$toast('意见反馈功能开发中...')
    },

    goToAbout() {
      this.$dialog.alert({
        title: '关于我们',
        message: '京航金融管理系统\n版本: v1.0.0\n© 2024 京航金融 版权所有'
      })
    },

    clearCache() {
      this.$dialog.confirm({
        title: '提示',
        message: '确定要清除缓存吗？'
      }).then(() => {
        // 清除缓存逻辑
        this.cacheSize = '0MB'
        this.$toast.success('缓存清除成功')
      }).catch(() => {
        // 用户取消操作
      })
    },

    handleLogout() {
      this.$dialog.confirm({
        title: '提示',
        message: '确定要退出登录吗？'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$router.push('/mobile/login')
          this.$toast.success('已退出登录')
        })
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-profile {
  background-color: #f5f5f5;
  min-height: calc(100vh - 110px);
}

.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px 20px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .user-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      margin-right: 20px;
      cursor: pointer;
      border: 3px solid rgba(255, 255, 255, 0.3);
    }

    .info {
      .name {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .role {
        font-size: 14px;
        opacity: 0.9;
        margin-bottom: 3px;
      }

      .department {
        font-size: 12px;
        opacity: 0.7;
      }
    }
  }

  .edit-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    i {
      font-size: 18px;
    }
  }
}

.stats-section {
  background: white;
  margin: 15px;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-around;

  .stat-item {
    text-align: center;

    .value {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 5px;
    }

    .label {
      font-size: 12px;
      color: #666;
    }
  }
}

.menu-section {
  margin: 0 15px;

  .menu-group {
    background: white;
    border-radius: 12px;
    margin-bottom: 15px;
    overflow: hidden;

    .menu-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.2s;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: #f5f5f5;
      }

      .menu-left {
        display: flex;
        align-items: center;

        i {
          font-size: 18px;
          color: #666;
          margin-right: 15px;
          width: 20px;
        }

        span {
          font-size: 16px;
          color: #333;
        }
      }

      .cache-size {
        font-size: 14px;
        color: #999;
      }

      .el-icon-arrow-right {
        color: #ccc;
        font-size: 14px;
      }
    }
  }
}

.logout-section {
  margin: 20px 15px;

  .logout-btn {
    width: 100%;
    height: 50px;
    font-size: 16px;
    border-radius: 25px;
  }
}

.version-info {
  text-align: center;
  padding: 20px;
  color: #999;

  p {
    margin: 5px 0;
    font-size: 12px;
  }
}

// Vant 弹窗样式
.edit-popup {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .popup-header {
    text-align: center;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .popup-content {
    flex: 1;
    overflow-y: auto;
  }

  .popup-footer {
    margin-top: 20px;
    padding-top: 20px;
  }
}

// Vant 表单样式覆盖
::v-deep .van-field__label {
  width: 80px;
  color: #333;
  font-weight: 500;
}

::v-deep .van-field__control {
  color: #333;
}

::v-deep .van-button--danger {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border: none;
}
</style>
